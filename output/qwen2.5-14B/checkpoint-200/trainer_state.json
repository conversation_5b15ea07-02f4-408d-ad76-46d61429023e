{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 1.8181818181818183, "eval_steps": 500, "global_step": 200, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09090909090909091, "grad_norm": 1.8492499589920044, "learning_rate": 9.972727272727273e-05, "loss": 1.8766, "step": 10}, {"epoch": 0.18181818181818182, "grad_norm": 0.30797022581100464, "learning_rate": 9.942424242424243e-05, "loss": 0.226, "step": 20}, {"epoch": 0.2727272727272727, "grad_norm": 0.2393084019422531, "learning_rate": 9.912121212121213e-05, "loss": 0.0734, "step": 30}, {"epoch": 0.36363636363636365, "grad_norm": 0.22016793489456177, "learning_rate": 9.881818181818182e-05, "loss": 0.0502, "step": 40}, {"epoch": 0.45454545454545453, "grad_norm": 0.2944433391094208, "learning_rate": 9.851515151515151e-05, "loss": 0.0478, "step": 50}, {"epoch": 0.5454545454545454, "grad_norm": 0.15977256000041962, "learning_rate": 9.821212121212122e-05, "loss": 0.0408, "step": 60}, {"epoch": 0.6363636363636364, "grad_norm": 0.16223767399787903, "learning_rate": 9.790909090909091e-05, "loss": 0.0367, "step": 70}, {"epoch": 0.7272727272727273, "grad_norm": 0.16335462033748627, "learning_rate": 9.760606060606062e-05, "loss": 0.0393, "step": 80}, {"epoch": 0.8181818181818182, "grad_norm": 0.15953192114830017, "learning_rate": 9.730303030303031e-05, "loss": 0.036, "step": 90}, {"epoch": 0.9090909090909091, "grad_norm": 0.1014583557844162, "learning_rate": 9.7e-05, "loss": 0.0339, "step": 100}, {"epoch": 1.0, "grad_norm": 0.12201367318630219, "learning_rate": 9.669696969696969e-05, "loss": 0.0334, "step": 110}, {"epoch": 1.0909090909090908, "grad_norm": 0.13768769800662994, "learning_rate": 9.63939393939394e-05, "loss": 0.0325, "step": 120}, {"epoch": 1.1818181818181819, "grad_norm": 0.11498506367206573, "learning_rate": 9.60909090909091e-05, "loss": 0.03, "step": 130}, {"epoch": 1.2727272727272727, "grad_norm": 0.12020551413297653, "learning_rate": 9.57878787878788e-05, "loss": 0.0324, "step": 140}, {"epoch": 1.3636363636363638, "grad_norm": 0.11689705401659012, "learning_rate": 9.548484848484849e-05, "loss": 0.0313, "step": 150}, {"epoch": 1.4545454545454546, "grad_norm": 0.1433083862066269, "learning_rate": 9.518181818181818e-05, "loss": 0.0311, "step": 160}, {"epoch": 1.5454545454545454, "grad_norm": 0.08304588496685028, "learning_rate": 9.487878787878788e-05, "loss": 0.0316, "step": 170}, {"epoch": 1.6363636363636362, "grad_norm": 0.09650571644306183, "learning_rate": 9.457575757575759e-05, "loss": 0.0303, "step": 180}, {"epoch": 1.7272727272727273, "grad_norm": 0.12060098350048065, "learning_rate": 9.427272727272728e-05, "loss": 0.0302, "step": 190}, {"epoch": 1.8181818181818183, "grad_norm": 0.09045235067605972, "learning_rate": 9.396969696969697e-05, "loss": 0.0294, "step": 200}], "logging_steps": 10, "max_steps": 3300, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 8.190105219793306e+16, "train_batch_size": 4, "trial_name": null, "trial_params": null}
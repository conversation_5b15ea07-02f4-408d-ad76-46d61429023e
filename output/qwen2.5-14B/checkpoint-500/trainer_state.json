{"best_global_step": null, "best_metric": null, "best_model_checkpoint": null, "epoch": 4.545454545454545, "eval_steps": 500, "global_step": 500, "is_hyper_param_search": false, "is_local_process_zero": true, "is_world_process_zero": true, "log_history": [{"epoch": 0.09090909090909091, "grad_norm": 1.8492499589920044, "learning_rate": 9.972727272727273e-05, "loss": 1.8766, "step": 10}, {"epoch": 0.18181818181818182, "grad_norm": 0.30797022581100464, "learning_rate": 9.942424242424243e-05, "loss": 0.226, "step": 20}, {"epoch": 0.2727272727272727, "grad_norm": 0.2393084019422531, "learning_rate": 9.912121212121213e-05, "loss": 0.0734, "step": 30}, {"epoch": 0.36363636363636365, "grad_norm": 0.22016793489456177, "learning_rate": 9.881818181818182e-05, "loss": 0.0502, "step": 40}, {"epoch": 0.45454545454545453, "grad_norm": 0.2944433391094208, "learning_rate": 9.851515151515151e-05, "loss": 0.0478, "step": 50}, {"epoch": 0.5454545454545454, "grad_norm": 0.15977256000041962, "learning_rate": 9.821212121212122e-05, "loss": 0.0408, "step": 60}, {"epoch": 0.6363636363636364, "grad_norm": 0.16223767399787903, "learning_rate": 9.790909090909091e-05, "loss": 0.0367, "step": 70}, {"epoch": 0.7272727272727273, "grad_norm": 0.16335462033748627, "learning_rate": 9.760606060606062e-05, "loss": 0.0393, "step": 80}, {"epoch": 0.8181818181818182, "grad_norm": 0.15953192114830017, "learning_rate": 9.730303030303031e-05, "loss": 0.036, "step": 90}, {"epoch": 0.9090909090909091, "grad_norm": 0.1014583557844162, "learning_rate": 9.7e-05, "loss": 0.0339, "step": 100}, {"epoch": 1.0, "grad_norm": 0.12201367318630219, "learning_rate": 9.669696969696969e-05, "loss": 0.0334, "step": 110}, {"epoch": 1.0909090909090908, "grad_norm": 0.13768769800662994, "learning_rate": 9.63939393939394e-05, "loss": 0.0325, "step": 120}, {"epoch": 1.1818181818181819, "grad_norm": 0.11498506367206573, "learning_rate": 9.60909090909091e-05, "loss": 0.03, "step": 130}, {"epoch": 1.2727272727272727, "grad_norm": 0.12020551413297653, "learning_rate": 9.57878787878788e-05, "loss": 0.0324, "step": 140}, {"epoch": 1.3636363636363638, "grad_norm": 0.11689705401659012, "learning_rate": 9.548484848484849e-05, "loss": 0.0313, "step": 150}, {"epoch": 1.4545454545454546, "grad_norm": 0.1433083862066269, "learning_rate": 9.518181818181818e-05, "loss": 0.0311, "step": 160}, {"epoch": 1.5454545454545454, "grad_norm": 0.08304588496685028, "learning_rate": 9.487878787878788e-05, "loss": 0.0316, "step": 170}, {"epoch": 1.6363636363636362, "grad_norm": 0.09650571644306183, "learning_rate": 9.457575757575759e-05, "loss": 0.0303, "step": 180}, {"epoch": 1.7272727272727273, "grad_norm": 0.12060098350048065, "learning_rate": 9.427272727272728e-05, "loss": 0.0302, "step": 190}, {"epoch": 1.8181818181818183, "grad_norm": 0.09045235067605972, "learning_rate": 9.396969696969697e-05, "loss": 0.0294, "step": 200}, {"epoch": 1.9090909090909092, "grad_norm": 0.09499497711658478, "learning_rate": 9.366666666666668e-05, "loss": 0.0289, "step": 210}, {"epoch": 2.0, "grad_norm": 0.1412694901227951, "learning_rate": 9.336363636363637e-05, "loss": 0.0306, "step": 220}, {"epoch": 2.090909090909091, "grad_norm": 0.07607545703649521, "learning_rate": 9.306060606060607e-05, "loss": 0.0294, "step": 230}, {"epoch": 2.1818181818181817, "grad_norm": 0.10216839611530304, "learning_rate": 9.275757575757576e-05, "loss": 0.0283, "step": 240}, {"epoch": 2.2727272727272725, "grad_norm": 0.08914034813642502, "learning_rate": 9.245454545454546e-05, "loss": 0.0284, "step": 250}, {"epoch": 2.3636363636363638, "grad_norm": 0.08603155612945557, "learning_rate": 9.215151515151516e-05, "loss": 0.0281, "step": 260}, {"epoch": 2.4545454545454546, "grad_norm": 0.0861383005976677, "learning_rate": 9.184848484848485e-05, "loss": 0.0293, "step": 270}, {"epoch": 2.5454545454545454, "grad_norm": 0.10202424973249435, "learning_rate": 9.154545454545454e-05, "loss": 0.0291, "step": 280}, {"epoch": 2.6363636363636362, "grad_norm": 0.0797269195318222, "learning_rate": 9.124242424242425e-05, "loss": 0.0275, "step": 290}, {"epoch": 2.7272727272727275, "grad_norm": 0.06945421546697617, "learning_rate": 9.093939393939394e-05, "loss": 0.0279, "step": 300}, {"epoch": 2.8181818181818183, "grad_norm": 0.07558063417673111, "learning_rate": 9.063636363636365e-05, "loss": 0.0291, "step": 310}, {"epoch": 2.909090909090909, "grad_norm": 0.08054812997579575, "learning_rate": 9.033333333333334e-05, "loss": 0.0303, "step": 320}, {"epoch": 3.0, "grad_norm": 0.0736413225531578, "learning_rate": 9.003030303030303e-05, "loss": 0.0293, "step": 330}, {"epoch": 3.090909090909091, "grad_norm": 0.08140678703784943, "learning_rate": 8.972727272727272e-05, "loss": 0.0281, "step": 340}, {"epoch": 3.1818181818181817, "grad_norm": 0.07667595148086548, "learning_rate": 8.942424242424243e-05, "loss": 0.0285, "step": 350}, {"epoch": 3.2727272727272725, "grad_norm": 0.08115892857313156, "learning_rate": 8.912121212121213e-05, "loss": 0.0278, "step": 360}, {"epoch": 3.3636363636363638, "grad_norm": 0.10285519063472748, "learning_rate": 8.881818181818182e-05, "loss": 0.0275, "step": 370}, {"epoch": 3.4545454545454546, "grad_norm": 0.08881440758705139, "learning_rate": 8.851515151515152e-05, "loss": 0.0283, "step": 380}, {"epoch": 3.5454545454545454, "grad_norm": 0.08911091834306717, "learning_rate": 8.82121212121212e-05, "loss": 0.0283, "step": 390}, {"epoch": 3.6363636363636362, "grad_norm": 0.07225844264030457, "learning_rate": 8.790909090909091e-05, "loss": 0.0275, "step": 400}, {"epoch": 3.7272727272727275, "grad_norm": 0.0717458426952362, "learning_rate": 8.760606060606062e-05, "loss": 0.0291, "step": 410}, {"epoch": 3.8181818181818183, "grad_norm": 0.07169827073812485, "learning_rate": 8.730303030303031e-05, "loss": 0.0283, "step": 420}, {"epoch": 3.909090909090909, "grad_norm": 0.07786688208580017, "learning_rate": 8.7e-05, "loss": 0.0277, "step": 430}, {"epoch": 4.0, "grad_norm": 0.053810082376003265, "learning_rate": 8.669696969696969e-05, "loss": 0.0284, "step": 440}, {"epoch": 4.090909090909091, "grad_norm": 0.06969082355499268, "learning_rate": 8.63939393939394e-05, "loss": 0.0267, "step": 450}, {"epoch": 4.181818181818182, "grad_norm": 0.08510831743478775, "learning_rate": 8.60909090909091e-05, "loss": 0.0266, "step": 460}, {"epoch": 4.2727272727272725, "grad_norm": 0.06832525879144669, "learning_rate": 8.57878787878788e-05, "loss": 0.0268, "step": 470}, {"epoch": 4.363636363636363, "grad_norm": 0.07024388760328293, "learning_rate": 8.548484848484849e-05, "loss": 0.0275, "step": 480}, {"epoch": 4.454545454545454, "grad_norm": 0.07130441069602966, "learning_rate": 8.518181818181819e-05, "loss": 0.0271, "step": 490}, {"epoch": 4.545454545454545, "grad_norm": 0.06362784653902054, "learning_rate": 8.487878787878788e-05, "loss": 0.0282, "step": 500}], "logging_steps": 10, "max_steps": 3300, "num_input_tokens_seen": 0, "num_train_epochs": 30, "save_steps": 100, "stateful_callbacks": {"TrainerControl": {"args": {"should_epoch_stop": false, "should_evaluate": false, "should_log": false, "should_save": true, "should_training_stop": false}, "attributes": {}}}, "total_flos": 2.047899112636293e+17, "train_batch_size": 4, "trial_name": null, "trial_params": null}
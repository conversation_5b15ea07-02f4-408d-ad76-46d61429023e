# -*- coding: utf-8 -*-
import os
import torch
import pandas as pd
from datasets import Dataset
from peft import LoraConfig, TaskType, get_peft_model
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    TrainingArguments,
    Trainer,
    DataCollatorForSeq2Seq
)
# from swanlab.integration.transformers import SwanLabCallback
# import swanlab

# ✅ 设备设置
os.environ["CUDA_VISIBLE_DEVICES"] = "5"
device = "cuda" if torch.cuda.is_available() else "cpu"
print(f"✅ 使用设备: {device}")

# ✅ 模型路径和训练集路径
model_dir = "/opt/LLM_MODEL/deepseek_1.3b/Qwen/Qwen2.5-14B-Instruct/"
train_path = "./training_dataset_2000.json"  # 你的AI客服训练数据路径

# 在 imports 后立即添加
print("✅ 环境检查开始")
print(f"PyTorch 版本: {torch.__version__}")
print(f"CUDA 可用: {torch.cuda.is_available()}")
print(f"当前设备: {device}")
print(f"可见 GPU: {os.environ.get('CUDA_VISIBLE_DEVICES')}")

if not os.path.exists(train_path):
    raise FileNotFoundError(f"训练数据不存在: {train_path}")


# ✅ 加载 tokenizer 和模型
tokenizer = AutoTokenizer.from_pretrained(model_dir, use_fast=False, trust_remote_code=True)

# 修复 meta tensor 问题：不使用 device_map="auto"，手动指定设备
model = AutoModelForCausalLM.from_pretrained(
    model_dir,
    torch_dtype=torch.bfloat16,
    trust_remote_code=True,
    low_cpu_mem_usage=True,
    device_map=None  # 不使用自动设备映射
)

# 手动将模型移动到指定设备
model = model.to(device)
model.enable_input_require_grads()

# ✅ 数据预处理函数
def process_func(example):
    prompt = tokenizer.apply_chat_template([
        {"role": "system", "content": example["instruction"]},
        {"role": "user", "content": example["input"]},
        {"role": "assistant", "content": example["output"]},
    ], tokenize=False, add_generation_prompt=False)

    tokenized = tokenizer(prompt, truncation=True, max_length=1024)
    input_ids = tokenized["input_ids"]
    attention_mask = tokenized["attention_mask"]
    labels = input_ids.copy()

    return {
        "input_ids": input_ids,
        "attention_mask": attention_mask,
        "labels": labels
    }

# ✅ 加载数据
df = pd.read_json(train_path)
ds = Dataset.from_pandas(df)
train_dataset = ds.map(process_func, remove_columns=ds.column_names, num_proc=4)

# ✅ LoRA 配置
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"],
    inference_mode=False,
    r=8,
    lora_alpha=32,
    lora_dropout=0.1,
)
model = get_peft_model(model, lora_config)

# ✅ 训练参数
train_args = TrainingArguments(
    output_dir="./output/qwen2.5-14B",
    per_device_train_batch_size=4,
    gradient_accumulation_steps=4,
    logging_steps=10,
    num_train_epochs=30,
    save_steps=100,
    learning_rate=1e-4,
    save_on_each_node=True,
    gradient_checkpointing=True,
    report_to="none",
    device=device,  # 明确指定设备
)

# ✅ SwanLab 日志记录 (暂时注释掉，需要安装 swanlab)
# swanlab_callback = SwanLabCallback(
#     project="Qwen2.5",
#     experiment_name="Qwen2.5-14B-FunctionCalling",
#     description="使用 Qwen2.5-14B-Instruct 微调意图识别",
#     config={
#         "model": "Qwen2.5-14B-Instruct",
#         "dataset": "training_dataset_2000.json"
#     }
# )

# ✅ 启动训练
trainer = Trainer(
    model=model,
    args=train_args,
    train_dataset=train_dataset,
    data_collator=DataCollatorForSeq2Seq(tokenizer=tokenizer, padding=True),
    # callbacks=[swanlab_callback],  # 暂时注释掉
)

trainer.train()
# swanlab.finish()  # 暂时注释掉

#!/usr/bin/env python3
"""
Qwen2.5-7B-Instruct智能家居控制模型双GPU vLLM性能测试脚本
专门处理双GPU并行配置
"""

import json
import time
import os
import torch
from typing import Dict, List, Tuple, Any
from transformers import AutoTokenizer

# 设置环境变量
os.environ["CUDA_VISIBLE_DEVICES"] = "5,6"
os.environ["RAY_DEDUP_LOGS"] = "0"

# 导入vLLM
from vllm import LLM, SamplingParams

class DualGPUVLLMTester:
    def __init__(self):
        """初始化双GPU vLLM测试器"""
        self.base_model_path = "/opt/LLM_MODEL/Qwen2-1.5B-Instruct/qwen/Qwen2-1.5B-Instruct/"
        self.finetuned_model_path = "/opt/fuyu/test/output/Qwen1.5/checkpoint-100"
        
        # 简化的系统提示词
        self.system_prompt = """你是智能家居控制助手，必须严格遵守以下规则来响应用户的请求：

1. 所有输出必须是一个**标准 JSON 对象**，且包含名为 `"function_calls"` 的字段，其值为一个 JSON 数组。

2. 设备控制强制规则：
   1. **room 参数必须精确匹配**：
      - 只返回用户明确提到的位置词（如"客厅"、"卧室"）
      - 未提及位置时返回"默认"

   2. **禁止位置推断**：
      - 示例：用户说"打开床头灯"
        - 正确：room = "默认"
        - 错误：room = "卧室"（禁止推断）

   3. **全屋规则**：
      - 仅当用户明确说"全屋"、"所有设备"时才返回"all"

3. 每个数组元素是一个合法的函数调用对象，结构如下：
   {
     "name": "函数名",
     "arguments": {
       "intent": "意图，来自预定义枚举列表[\"打开插座\", \"关闭插座\",\"打开开关\",\"关闭开关\",\"打开灯\",\"关闭灯\",\"打开窗帘\",\"关闭窗帘\",\"暂停窗帘\",\"打开通断器\",\"关闭通断器\",\"打开安防\",\"关闭安防\",\"打开空调\",\"关闭空调\",\"打开新风\",\"关闭新风\",\"打开杀菌\",\"关闭杀菌\",\"打开地暖\",\"关闭地暖\",\"设置亮度\",\"调高亮度\",\"调低亮度\",\"设置色温\",\"调高色温\",\"调低色温\",\"设置开合度\",\"调大开合度\",\"调小开合度\",\"设置温度\",\"调高温度\",\"调低温度\",\"设置风速\",\"调高风速\",\"调低风速\",\"调高地暖\",\"调低地暖\",\"设置地暖温度\",\"打开场景\",\"查询限行\",\"查询化妆指数\",\"查询紫外线指数\",\"查询感冒指数\",\"查询洗车指数\",\"查询穿衣指数\",\"查询运动指数\",\"查询钓鱼指数\",\"闲聊\",\"设备数量查询\",\"终止对话\",\"重新开始\",\"敏感词\",\"自我介绍\",\"查询空气质量&空气污染扩散指数\",\"查询空气湿度\",\"查询温度/体感温度\",\"查询风速/风向\",\"查询天气状况\",\"查询日出/日落时间\"]",
       "content": "回答用户问题，用于闲聊对话，严格按照纯文本输出",
       "domain": "意图域，来自预定义枚举列表[\"插座\",\"通断器\",\"灯\",\"开关\",\"窗帘\",\"空调\",\"新风\",\"地暖\",\"场景\",\"天气\",\"生活指数\",\"闲聊\",\"对话\",\"\"]；若未指定，默认为'默认'",
       "value": "设置值（仅 setHighOrLow 函数需要）",
       "room": "空间位置，若用户提到\"全屋\"、\"全部\"、\"所有\"，返回 \"all\"；未明确则默认为 \"默认\"",
       "device": "设备昵称，用于描述用户指定的设备昵称，若设备昵称和domain参数值一致，则返回空字符串",
       "scene": "场景名称，如离家/回家模式，开灯/关灯，打开/关闭全屋灯",
       "pos": "查询天气或生活指数的地点，默认为顺德",
       "offset": "查询时间偏移量规则，若为具体某一天，必须带符号：今天 → '+0', 明天 → '+1', 后天 → '+2', 昨天 → '-1'",
       "unit": "查询天气或者生活指数的时间单位，默认为day，来自预定义枚举列表[\"pos\", \"year\", \"month\", \"day\", \"week\", \"hour\", \"minute\", \"second\",\"timeRange\"]"
     }
   }

4. 必须从以下函数中选择合适的进行调用：
   - openOrClose：用于开关类操作
   - setHighOrLow：用于调节亮度、色温、风速、温度、开合度等
   - scene：用于执行场景（如回家模式）
   - getWeather：查询天气信息
   - getLiving：查询生活指数
   - queryDevice：查询设备数量或状态
   - chat：处理闲聊类对话
   - dialog：处理对话意图（终止、重新开始、敏感词）
   - xiaoling：自我介绍

5. 参数要求：
   - intent 必须从预定义枚举列表中选择，不能随意构造
   - domain 必须匹配函数支持的设备类型
   - value 只能是数字或数字+百分号（%），不允许带单位文字
   - device 若和 domain 相同则返回空字符串，否则返回具体设备昵称

6. 输出必须是纯 JSON，不带任何解释、注释、Markdown 或多余字段。

7. 最关键的是判断意图，最好不要漏分析用户的意图，
· 1. 比如：用户的输入"帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"，可以分为三个意图：
   （1）把卧室的灯调高亮度    
   （2）打开客厅的窗帘
   （3）启动回家模式

   2. 比如用户的输入："打开客厅东空调工作模式调成制热温度调到二十六度风速调成高速"，可以分为4个意图
   （1）打开客厅东空调
   （2）客厅东空调的工作模式调整为制热
   （3）调整温度到26度
   （4）调整风速调整为高速

8. 多个意图需生成多个 function_call，按语义顺序排列。

示例输入："帮我把卧室的灯调高亮度，再打开客厅的窗帘，并启动回家模式"
示例输出：
{
  "function_calls": [
    {
      "name": "setHighOrLow",
      "arguments": {
        "intent": "调高亮度",
        "domain": "灯",
        "value": "",
        "room": "卧室",
        "device": ""
      }
    },
    {
      "name": "openOrClose",
      "arguments": {
        "intent": "打开窗帘",
        "domain": "窗帘",
        "room": "客厅",
        "device": ""
      }
    },
    {
      "name": "scene",
      "arguments": {
        "intent": "打开场景",
        "domain": "场景",
        "scene": "回家模式"
      }
    }
  ]
}"""

        print("🚀 正在初始化双GPU vLLM测试器...")
        self.load_model()
        
    def load_model(self):
        """加载双GPU vLLM模型"""
        try:
            print("📥 正在加载tokenizer...")
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.base_model_path, 
                use_fast=False, 
                trust_remote_code=True
            )
            
            print("📥 正在加载双GPU vLLM微调模型...")
            print("⚠️  注意：双GPU初始化可能需要较长时间，请耐心等待...")

            # 双GPU配置，启用LoRA支持
            self.model = LLM(
                model=self.base_model_path,
                tensor_parallel_size=2,  # 双GPU并行
                trust_remote_code=True,
                dtype="float16",
                gpu_memory_utilization=0.8,  # 保守的内存使用
                max_model_len=2048,
                disable_log_stats=True,
                enable_prefix_caching=True,
                max_num_batched_tokens=4096,
                max_num_seqs=32,  # 较小的序列数避免内存问题
                disable_custom_all_reduce=True,
                enable_lora=True,  # 启用LoRA支持
                max_loras=1,  # 最大LoRA适配器数量
                max_lora_rank=64  # LoRA rank
            )

            print("📥 正在加载LoRA微调适配器...")
            # 检查LoRA适配器是否可用
            try:
                # vLLM的LoRA支持可能需要特殊处理
                self.lora_available = True
                print("✅ LoRA适配器准备就绪！")
            except Exception as e:
                print(f"⚠️ LoRA适配器准备失败: {e}")
                print("将使用基础模型进行测试")
                self.lora_available = False
            
            # 配置采样参数
            self.sampling_params = SamplingParams(
                temperature=0.8,#
                max_tokens=512,
                skip_special_tokens=True,
                stop_token_ids=[self.tokenizer.eos_token_id] if self.tokenizer.eos_token_id else None
            )
            
            print("✅ 双GPU vLLM模型加载完成！")
            
        except Exception as e:
            print(f"❌ 双GPU vLLM模型加载失败: {e}")
            raise
    
    def predict_vllm(self, messages: List[Dict]) -> Tuple[str, float]:
        """使用双GPU vLLM进行微调模型预测"""
        try:
            start_time = time.time()

            # 构建输入文本
            text = self.tokenizer.apply_chat_template(
                messages,
                tokenize=False,
                add_generation_prompt=True
            )

            # 准备LoRA请求（如果可用）
            lora_request = None
            if self.lora_available:
                try:
                    from vllm.lora.request import LoRARequest
                    lora_request = LoRARequest("finetuned_adapter", 1, self.finetuned_model_path)
                except ImportError:
                    print("⚠️ 当前vLLM版本不支持LoRA，使用基础模型")
                    lora_request = None

            # vLLM推理
            outputs = self.model.generate([text], self.sampling_params, lora_request=lora_request)
            response = outputs[0].outputs[0].text.strip()
            inference_time = time.time() - start_time

            return response, inference_time

        except Exception as e:
            print(f"❌ 双GPU vLLM预测失败: {e}")
            return f"ERROR: {str(e)}", 0.0

    def evaluate_json_format(self, response: str) -> bool:
        """评估JSON格式正确性"""
        try:
            json.loads(response)
            return True
        except:
            return False

    def evaluate_function_calls(self, response: str) -> Dict[str, Any]:
        """评估function_calls结构正确性"""
        result = {
            "has_function_calls": False,
            "function_calls_count": 0,
            "valid_structure": False,
            "functions": []
        }

        try:
            data = json.loads(response)
            if "function_calls" in data and isinstance(data["function_calls"], list):
                result["has_function_calls"] = True
                result["function_calls_count"] = len(data["function_calls"])
                result["valid_structure"] = True

                for func_call in data["function_calls"]:
                    if isinstance(func_call, dict) and "name" in func_call and "arguments" in func_call:
                        result["functions"].append(func_call["name"])
                    else:
                        result["valid_structure"] = False
                        break
        except:
            pass

        return result

    def performance_test(self):
        """双GPU性能测试"""
        print("\n🎯 双GPU vLLM微调模型性能测试模式")
        print("=" * 60)

        # 测试用例
        test_cases = [
            "打开客厅的灯",
            "关闭卧室的空调",
            "把客厅温度调到26度",
            "灯亮度和色温调低一点",
            "灯亮度调到百分之八十，色温调到三千",
            "空调的温度调到二十六度模式调成制热风速调高",
            "打开客厅东空调工作模式调成制热温度调到二十六度风速调成高速",
            "明天天气怎么样",
            "主卧的灯亮度亮度设成80%",
            "客厅空调的温度调到26度模式调成制热风速调高",
            "打开全屋的灯",
            "打开主卧的灯然后亮度调到80%色温调到3700k",
            "启动回家模式"
        ]

        performance_stats = {
            "times": [],
            "json_success": 0,
            "total_tests": 0
        }

        for i, user_input in enumerate(test_cases, 1):
            print(f"\n📝 测试 {i}/{len(test_cases)}: {user_input}")
            print("-" * 40)

            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            # 双GPU vLLM预测
            response, inference_time = self.predict_vllm(messages)
            print(f"响应时间: {inference_time:.3f}s")
            print(f"输出: {response}")

            # 评估
            json_valid = self.evaluate_json_format(response)
            func_eval = self.evaluate_function_calls(response)

            print(f"JSON格式: {'✅' if json_valid else '❌'}")
            print(f"Function调用: {'✅' if func_eval['valid_structure'] else '❌'} ({func_eval['function_calls_count']}个)")

            # 更新统计
            performance_stats["times"].append(inference_time)
            performance_stats["json_success"] += 1 if json_valid else 0
            performance_stats["total_tests"] += 1

        # 显示统计结果

        self.show_performance_summary(performance_stats)

    def show_performance_summary(self, stats: Dict):
        """显示性能总结"""
        print("\n📈 双GPU vLLM微调模型性能总结报告")
        print("=" * 60)
        
        # 时间统计
        avg_time = sum(stats["times"]) / len(stats["times"])
        min_time = min(stats["times"])
        max_time = max(stats["times"])
        
        print(f"⏱️  推理时间统计:")
        print(f"   平均时间: {avg_time:.3f}s")
        print(f"   最快时间: {min_time:.3f}s")
        print(f"   最慢时间: {max_time:.3f}s")
        
        # JSON格式成功率
        json_rate = stats["json_success"] / stats["total_tests"] * 100
        
        print(f"\n✅ JSON格式成功率: {json_rate:.1f}%")
        print(f"📊 总测试次数: {stats['total_tests']}")
        print("=" * 60)

    def interactive_test(self):
        """交互式测试"""
        print("\n🎯 双GPU微调模型交互式测试模式")
        print("输入智能家居控制指令，输入 'quit' 退出")
        print("=" * 60)

        while True:
            user_input = input("\n请输入指令: ").strip()
            if user_input.lower() in ['quit', 'exit', '退出']:
                break

            if not user_input:
                continue

            # 构建消息
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": user_input}
            ]

            print(f"\n📝 用户输入: {user_input}")
            print("-" * 40)

            # 双GPU vLLM预测
            response, inference_time = self.predict_vllm(messages)
            print(f"响应时间: {inference_time:.3f}s")
            print(f"输出: {response}")

            # 评估
            json_valid = self.evaluate_json_format(response)
            func_eval = self.evaluate_function_calls(response)

            print(f"JSON格式: {'✅' if json_valid else '❌'}")
            print(f"Function调用: {'✅' if func_eval['valid_structure'] else '❌'} ({func_eval['function_calls_count']}个)")
            print("=" * 60)


def main():
    """主函数"""
    print("🤖 Qwen2.5-7B-Instruct 双GPU vLLM微调模型性能测试工具")
    print("=" * 60)

    try:
        # 初始化双GPU测试器
        tester = DualGPUVLLMTester()

        # 选择测试模式
        print("\n请选择测试模式:")
        print("1. 自动性能测试")
        print("2. 交互式测试")
        
        choice = input("请输入选择 (1/2): ").strip()
        
        if choice == "1":
            tester.performance_test()
        elif choice == "2":
            tester.interactive_test()
        else:
            print("无效选择，默认运行自动性能测试")
            tester.performance_test()

    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
